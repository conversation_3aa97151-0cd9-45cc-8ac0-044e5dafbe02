{"userStories": [{"id": "US-001", "title": "User Login", "description": "As a user, I want to log in securely so that I can access my account", "acceptanceCriteria": ["Valid credentials allow access", "Invalid credentials show error"]}, {"id": "US-002", "title": "Data Sync", "description": "As a user, I want my data to sync in real-time across devices", "acceptanceCriteria": ["Changes appear immediately", "Offline changes sync when online"]}]}